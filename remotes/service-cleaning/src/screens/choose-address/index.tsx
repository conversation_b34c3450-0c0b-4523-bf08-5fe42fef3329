/**
 * ChooseAddress Screen
 *
 * Displays a list of locations for the user to choose from when booking a cleaning service.
 * Allows users to select an address which will be used for the cleaning service.
 */
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { Animated, FlatList, ListRenderItem } from 'react-native';
import {
  AnimationHelpers,
  BlockView,
  ConfigHelpers,
  CText,
  IAddress,
  LocationItem,
  SizedBox,
  Spacing,
  useUserStore,
} from '@btaskee/design-system';
import { DEFAULT_LOCATION } from '@constant';
import { usePostTaskStore } from '@store';

import { useAppNavigation, useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';

import styles from './styles';

/**
 * ChooseAddress component that displays a list of locations for the user to select
 *
 * @param navigation - Navigation prop for navigating between screens
 */
export const ChooseAddress = memo(() => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { setAddress } = usePostTaskStore();
  const { user } = useUserStore();

  // Animation values for each item
  const fadeAnims = useRef<{ [key: string]: Animated.Value }>({}).current;

  const locations = useMemo(() => {
    if (ConfigHelpers.isE2ETesting) {
      return DEFAULT_LOCATION as IAddress[];
    }
    // TODO: de tam. co map thi xoa di. navigate qua map de chon dia chi neu chua dang nhap
    return user?.locations || (DEFAULT_LOCATION as IAddress[]);
  }, [user]);

  /**
   * Handles the selection of an address and navigates to the next screen
   *
   * @param selectedAddress - The location selected by the user
   */
  const onChooseAddress = useCallback(
    async (selectedAddress: IAddress) => {
      if (!selectedAddress) return;

      setAddress(selectedAddress);
      // postTask.set('address', selectedAddress);
      navigation.navigate(RouteName.ChooseDuration);
    },
    [navigation, setAddress],
  );

  /**
   * Creates or retrieves an animation value for a specific item
   *
   * @param index - The index of the item
   * @returns The animation value for the item
   */
  const getAnimationValue = useCallback(
    (index: number): Animated.Value => {
      const key = index.toString();
      if (!fadeAnims[key]) {
        fadeAnims[key] = new Animated.Value(0);
        // Start the animation with a staggered delay based on index
        Animated.timing(fadeAnims[key], {
          toValue: 1,
          duration: 400,
          delay: index * 100, // Stagger the animations
          useNativeDriver: true,
        }).start();
      }
      return fadeAnims[key];
    },
    [fadeAnims],
  );

  /**
   * Renders a location item in the FlatList with animation
   */
  const renderItem: ListRenderItem<IAddress> = useCallback(
    ({ item, index }) => {
      const fadeAnim = getAnimationValue(index);

      return (
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [
              {
                translateY: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0], // Slide up from 20px below
                }),
              },
            ],
          }}
        >
          <LocationItem
            testIDs={{
              item: `address${index + 1}`,
            }}
            shortAddress={item?.shortAddress || ''}
            address={item?.address || ''}
            onPress={() => onChooseAddress(item)}
            // onPressUpdate={() =>
            //   navigation.navigate(RouteName.EditLocation, {
            //     location: item,
            //   })
            // }
          />
        </Animated.View>
      );
    },
    [onChooseAddress, getAnimationValue],
  );

  /**
   * Renders a separator between list items
   */
  const itemSeparatorComponent = useCallback(
    () => <SizedBox height={16} />,
    [],
  );

  /**
   * Key extractor for the FlatList
   */
  const keyExtractor = useCallback((_, index: number) => index.toString(), []);

  // Apply layout animation when component mounts
  useEffect(() => {
    AnimationHelpers.runLayoutAnimation('easeInEaseOut', 500);
  }, []);

  return (
    <BlockView style={styles.container}>
      <BlockView style={styles.wrapFlatList}>
        <CText
          testID="address-list-title"
          bold
          style={styles.txtDescription}
        >
          {t('LIST_OF_LOCATIONS')}
        </CText>
        <SizedBox height={Spacing.SPACE_16} />
        <FlatList
          data={locations}
          keyExtractor={keyExtractor}
          renderItem={renderItem}
          showsVerticalScrollIndicator={false}
          testID={'scrollChooseAddress'}
          ItemSeparatorComponent={itemSeparatorComponent}
          contentContainerStyle={styles.contentContainer}
        />
      </BlockView>
    </BlockView>
  );
});
