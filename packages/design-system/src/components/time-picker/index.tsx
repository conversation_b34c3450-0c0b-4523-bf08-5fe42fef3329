/**
 * TimePicker component for selecting time values with a modal interface.
 * Provides a user-friendly way to select hours and minutes with validation against min/max constraints.
 */
import React, { useCallback, useEffect, useMemo } from 'react';
import { get } from 'lodash-es';

import {
  Alert,
  DateTimeHelpers,
  IDate,
  ITimezone,
  Maybe,
  roundOfNumberMinutes,
  TypeFormatDate,
} from '../../helpers';
import { useI18n } from '../../hooks';
import {
  ESTIMATED_TIME_POST_TASK_MINUTES,
  MIN_POST_TASK_TIME,
} from '../../utils';
import { BlockView } from '../block-view';
import { BlockViewProps } from '../block-view/types';
import { Icon } from '../icon';
import { CText } from '../text';
import { CommonTextProps } from '../text/types';
import { TouchableOpacity } from '../touchable-opacity';
import { ModalChooseTime } from './modal-choose-time';
import { styles } from './styles';

/**
 * Props for the TimePicker component
 */
export interface TimePickerProps {
  /** Current timezone */
  timezone: ITimezone;
  /** Test ID for component testing */
  testID?: string;
  /** Currently selected date/time value */
  value?: IDate;
  /** Callback when time changes */
  onChange?: (date: IDate, timezone: ITimezone) => void;
  /** Title text for the time picker */
  title?: Maybe<string>;
  /** Style for the container */
  style?: BlockViewProps['style'];
  /** Style for the button */
  buttonStyle?: BlockViewProps['style'];
  /** Style for the title text */
  titleStyle?: CommonTextProps['style'];
  /** Whether to hide the label */
  isHideLabel?: boolean;
  /** Style for the outer container */
  containerStyle?: BlockViewProps['style'];
  /** Style for the time display */
  timeStyle?: BlockViewProps['style'];
  /** Minimum allowed time */
  minTime?: IDate;
  /** Maximum allowed time */
  maxTime?: IDate;
  /** Whether to disable minimum time validation */
  isDisabledMinimumTime?: boolean;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** System settings */
  settingSystem: Record<string, any>;
}

/**
 * TimePicker component that displays a time selection interface
 */
export const TimePicker: React.FC<TimePickerProps> = (props) => {
  const {
    value,
    onChange,
    title,
    style,
    buttonStyle,
    titleStyle,
    isHideLabel = false,
    containerStyle = {},
    timeStyle = {},
    minTime,
    maxTime,
    isDisabledMinimumTime = false,
    timezone,
    disabled = false,
  } = props;

  // Get minimum post task time from settings or use default
  const minPostTaskTime = useMemo(() => {
    return (
      get(props, 'settingSystem.minPostTaskTime', MIN_POST_TASK_TIME) +
      ESTIMATED_TIME_POST_TASK_MINUTES
    );
  }, [props.settingSystem]);

  const testID = props.testID || '';
  const { t } = useI18n('common');

  // Calculate minimum date with rounded minutes
  const minimumDate = useMemo(() => {
    const minuteDate = roundOfNumberMinutes(
      DateTimeHelpers.getMinute({ timezone, date: minTime }),
    );

    return DateTimeHelpers.toDateTz({ timezone, date: minTime })
      .minute(minuteDate)
      .add(isDisabledMinimumTime ? 0 : minPostTaskTime, 'minute')
      .startOf('minute');
  }, [minTime, timezone, isDisabledMinimumTime, minPostTaskTime]);

  // Initialize with minimum date if no value provided
  useEffect(() => {
    if (!value && minimumDate && onChange) {
      onChangeDate(minimumDate);
    }
  }, []);

  /**
   * Handles date change and calls the onChange callback
   * @param date - The new date value
   */
  const onChangeDate = useCallback(
    (date: IDate) => {
      if (!onChange) return;

      const dateFormat = DateTimeHelpers.formatToString({ timezone, date });
      onChange(dateFormat, timezone);
    },
    [onChange, timezone],
  );

  /**
   * Opens the time picker modal
   */
  const handleOpen = useCallback(() => {
    if (disabled) return;

    Alert?.alert?.open({
      title: title ? title : t('CHOOSE_TIME'),
      message: (
        <ModalChooseTime
          timezone={timezone}
          date={value}
          minimumDate={minimumDate}
          maxTime={maxTime}
          onChangeDate={onChangeDate}
        />
      ),
    });
  }, [title, t, timezone, value, minimumDate, maxTime, onChangeDate, disabled]);

  // Memoize hour display to prevent unnecessary re-renders
  const hourDisplay = useMemo(() => {
    if (!value) return null;

    const hourTxt = DateTimeHelpers.formatToString({
      timezone,
      date: value,
      typeFormat: TypeFormatDate.TimeHour,
    });

    return (
      <CText
        bold
        testID={`hour${testID}`}
        style={styles.txtTime}
      >
        {hourTxt}
      </CText>
    );
  }, [value, timezone, testID]);

  // Memoize minute display to prevent unnecessary re-renders
  const minuteDisplay = useMemo(() => {
    if (!value) return null;

    const minuteTxt = DateTimeHelpers.formatToString({
      timezone,
      date: value,
      typeFormat: TypeFormatDate.TimeMinute,
    });

    return (
      <CText
        bold
        testID={`minute${testID}`}
        style={styles.txtTime}
      >
        {minuteTxt}
      </CText>
    );
  }, [value, timezone, testID]);

  return (
    <BlockView style={[styles.container, containerStyle]}>
      <BlockView
        jBetween
        row
        style={[styles.wrapperTime, style]}
      >
        {!isHideLabel && (
          <BlockView
            horizontal
            flex
            row
            style={styles.boxLabelTitle}
          >
            <Icon
              name={'icClock'}
              style={styles.clockIcon}
            />
            <BlockView flex>
              <CText
                bold
                style={[styles.txtDate, titleStyle]}
              >
                {title ? title : t('CHOOSE_TIME')}
              </CText>
            </BlockView>
          </BlockView>
        )}
        <BlockView
          row
          style={[styles.wrapperBtnTime, buttonStyle]}
        >
          <TouchableOpacity
            onPress={handleOpen}
            testID="time-picker"
            disabled={disabled}
          >
            <BlockView
              row
              style={[styles.btnTime, timeStyle]}
            >
              {hourDisplay}
              <CText style={styles.seperate} />
              {minuteDisplay}
            </BlockView>
          </TouchableOpacity>
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
