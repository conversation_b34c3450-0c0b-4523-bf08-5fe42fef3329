import { IDate } from '../../../helpers';
import { I<PERSON>ddons, IPrice, ITaskPlace, IUser } from '../../../types';

export class IParamsGetPriceCleaning {
  task: {
    timezone?: string;
    date: string;
    autoChooseTasker: boolean;
    taskPlace: ITaskPlace;
    homeType?: string;
    duration: number;
    forceTasker?: IUser;
    dateOptions?: IDate[];
    payment?: {
      method: string;
    };
    requirements?: { type: number }[];
    addons?: IAddons[];
    isPremium?: boolean;
  };
  service: {
    _id: string;
  };
  isoCode: string;
}

export class IGetPriceCleaningAPI {
  params?: IParamsGetPriceCleaning;
  response?: IPrice;
  error?: any;
}
