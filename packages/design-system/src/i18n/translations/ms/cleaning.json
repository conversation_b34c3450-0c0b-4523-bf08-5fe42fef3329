{"SV_HC_SCR2_DETAIL_BTN_COST_CURRENCY": "{{cost}} {{currency}}", "SV_HC_SCR2_DETAIL_EXTRA_SV_TITLE": "Perkhidmatan tambahan", "SV_HC_SCR2_DETAIL_EXTRA_SV_NOTE": "<PERSON>a boleh memilih perkhidmatan tambahan.", "BTASKEE": "b<PERSON><PERSON><PERSON>", "POST_TASK_STEP_2": {"LIST_TOOL_TASK_NORMAL": "Termasuk set alat biasa", "LIST_TOOL_TASK_PREMIUM": "Termasuk set alat standard dan penyedut debu pegang.", "TITLE_MODAL_LIST_TOOLS": "<PERSON><PERSON>, peralatan dan bahan kimia", "PREMIUM": "Premium", "OPTION_PET_FEE": "Untuk members<PERSON><PERSON> kawasan yang disediakan untuk haiwan peliharaan dengan baik, Tasker akan memer<PERSON>an alat dan bahan kimia yang khusus. Anda perlu membayar tambahan {{price}} jika memilih perkhidmatan ini.", "OPTION_PET_NOTE": "Sesetengah Tasker alah kepada bulu haiwan peliharaan. <PERSON><PERSON> mak<PERSON>kan dengan jelas tentang haiwan peliharaan anda agar Tasker dapat member<PERSON>n bantuan terbaik.", "SOME_RECOMMEND": "Beberapa cadangan untuk anda: ", "RECOMMEND_1": "- Sesetengah Tasker alah kepada bulu haiwan peliharaan. <PERSON><PERSON> mak<PERSON>kan dengan jelas tentang haiwan peliharaan anda agar Tasker dapat member<PERSON>n bantuan terbaik.", "RECOMMEND_2": "- Untuk memastikan keselamatan Tasker dan haiwan peliharaan anda, sila kekalkan haiwan peliharaan di dalam sangkar atau kawasan khas sepanjang proses kerja <PERSON>."}, "CLOSE": "<PERSON><PERSON><PERSON>", "MESSAGE_NAME_INVALID": "<PERSON>a tidak boleh mengandungi nombor dan aksara khas", "MESSAGE_EMAIL_INVALID": "<PERSON><PERSON> tidak sah", "PHONE_NUMBER_SYNTAX_IS_INCORRECT": "Format nombor telefon tidak betul", "ACCOUNT_ACTIVATION_ERROR_PASSWORD_LENGTH": "<PERSON><PERSON> 6 - 12 a<PERSON><PERSON>, tan<PERSON> ruang.", "THIS_FIELD_IS_REQUIRED": "Maklumat diperlukan", "MONEY_SYNTAX_INVALID": "<PERSON><PERSON><PERSON> wang tidak sah", "NUMBER_OF_HOURS": "{{t}} jam", "DURATION_TEXT": "<PERSON><PERSON><PERSON><PERSON> {{t1}} atau {{t2}}", "CONTENT_NOT_SUPPORT_CITY": "Perkhidmatan tidak tersedia di bandar ini.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3_TH": "Dua jam sebelum tugas bermula, jika anda tidak membuat pilihan, sistem secara automatik akan memilih Tasker dan yuran perkhidmatan tambahan masih akan dikenakan.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3": "<PERSON><PERSON> anda belum memilih Tasker satu jam sebelum kerja bermula, sistem akan memilih Tasker secara automatik. <PERSON> anda tetap akan dikenakan bayaran.", "PT1_DETAIL_CHOOSE_MANUAL_FEES": "Bayaran", "PT1_DETAIL_CHOOSE_MANUAL_COST_AND_CURRENCY": "{{cost}} {{currency}}", "PT1_DETAIL_OPTION_ITEM_CHOOSE_MANUAL": "<PERSON>a memilih Tasker secara manual", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_1": "Beberapa Tasker akan menerima kerja anda. <PERSON>a boleh memilih salah satu Tasker untuk bekerja.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_2": "Anda perlu membuat tempahan sekurang-kurangnya **{{hour}} jam** untuk menggunakan fungsi ini.", "PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD": "<PERSON><PERSON><PERSON>", "FAV_TASKER_TITLE": "<PERSON><PERSON><PERSON><PERSON> keutamaan Tasker kegemaran?", "FAV_TASKER_DESCRIPTION_EXPLAIN_1": "Ini adalah fungsi lalai apabila anda mempunyai Tasker k<PERSON>.", "FAV_TASKER_DESCRIPTION_EXPLAIN_2": "<PERSON><PERSON><PERSON> akan diutamakan kepada Tasker kegemaran. <PERSON>ka Tasker kegemaran sibuk atau tidak menerima kerja, kerja akan dihantar kepada Tasker be<PERSON>.", "BOOKING_STEP_2": {"CHOOSE_GENDER": "<PERSON><PERSON><PERSON><PERSON>", "GENDER_MALE": "<PERSON><PERSON><PERSON>", "GENDER_FEMALE": "Perempuan", "WHAT_IS_CHOOSE_GENDER": "<PERSON><PERSON><PERSON><PERSON> maksud memilih jantina <PERSON>?", "DESCRIPTION_CHOOSE_GENDER": "Bergantung kepada keperluan khusus anda, anda boleh memilih jantina Tasker untuk memastikan kualiti kerja dan memberikan kemudahan serta keselesaan yang lebih ketika menggunakan perkhidmatan."}, "PT1_DETAIL_OPTION_TITLE": "<PERSON><PERSON><PERSON>", "PT1_MAP_POPUP_ADD_PET_SUBMIT": "<PERSON><PERSON><PERSON>", "PT1_DETAIL_OPTION_HAVE_PET": "<PERSON><PERSON><PERSON> memp<PERSON>yai haiwan peli<PERSON>an", "COST_AND_CURRENCY": "{{cost}} {{currency}}", "OTHER": "Lain-lain", "PREMIUM_TITLE_OPTIONAL": "Pi<PERSON>h Perkhidmatan Premium", "PREMIUM_CONTENT_OPTIONAL": "Perkhidmatan Premium", "PREMIUM_DETAIL_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> oleh pasukan Tasker Premium, yang telah menjalani latihan lanjutan dan disahkan oleh bTaskee.", "PREMIUM_DETAIL_3": "Tasker Premium mesti mencapai standard sekurang-kurangnya 4.8 bintang dan ditapis berdasarkan ulasan pelanggan serta bTaskee.", "PREMIUM_DETAIL_1": "Apa itu Perkhidmatan Premium?", "NEXT": "Seterusnya", "UPDATE": "<PERSON><PERSON> kini", "INCLUDE_FEE_SHIP": "Termasuk yuran <PERSON>", "LAUNDRY_EMERGENCY_FEE": "{{t1}} {{t2}} (yuran bas<PERSON> kecema<PERSON>)", "TASK_DETAIL": "<PERSON><PERSON><PERSON> kerja", "ADD_ADDRESS": "<PERSON><PERSON> alamat baru", "SIGN_UP_NOW": "Log masuk sekarang", "LIST_OF_LOCATIONS": "<PERSON><PERSON><PERSON> lokasi", "SV_HC_SCR2_DETAIL_DURATION_TITLE_NOTE_TH": "Sila anggarkan dengan tepat luas dan masa yang diperlukan untuk membersihkan. <PERSON>ka anda memerlukan pembersihan untuk berpindah rumah, sila pertimbangkan untuk menetapkan lebih banyak jam berbanding pilihan yang dicadangkan di bawah.", "SV_HC_SCR2_DETAIL_DURATION_TITLE_NOTE": "<PERSON><PERSON> anggarkan saiz kawasan yang perlu dibersihkan dan pilih pilihan yang sesuai.", "SV_HC_SCR2_DETAIL_NOTE_INDO": "*Nota: Perkhidmatan ini bertujuan untuk pembersihan rutin harian dan tidak meliputi keperluan khusus seperti pasca-renovasi atau pembersihan pasca banjir.", "SV_HC_SCR2_DETAIL_NOTE": "*Nota: Perkhidmatan hanya menyokong maksimum 4 jam. <PERSON><PERSON> anda ingin membuat tempahan lebih, sila tempah ", "NOTE_POST_TASK_CLEANING_1": "perkhidmatan Pembersihan <PERSON>", "NOTE_POST_TASK_CLEANING_2": ", atau tempah 02 tugas dengan waktu yang hampir sama.", "SV_HC_SCR2_DETAIL_DURATION_TITLE": "Tempoh", "NUMBER_OF_ROOMS": "{{t}} bilik", "PT1_DETAIL_OPTION_ITEM_FAV_TASKER": "<PERSON><PERSON><PERSON><PERSON> kepada Tasker k<PERSON>n", "FAVORITE": "Kegemaran", "POST_TASK_BEFORE_HOUR": "Anda perlu menetapkan sekurang-kurangnya **{{hour}} jam** lebih awal untuk menggunakan fungsi **Anda memilih Tasker**. <PERSON><PERSON> pilih waktu lain.", "WORK_TIME": "<PERSON><PERSON> k<PERSON>", "CHOOSE_DATE": "<PERSON><PERSON><PERSON> tarikh kerja", "ELDERLY_CARE_CHOOSE_TIME_TITLE": "<PERSON><PERSON><PERSON> masa", "WORKING_SCHEDULE": "<PERSON><PERSON><PERSON> kerja", "CONFIRM": "<PERSON><PERSON><PERSON>", "CHILD_CARE_NOTE_TITLE": "Nota untuk pengasuh kanak-kanak", "TASK_NOTE_DESCRIPTION_CHILD_CARE": "Catatan terperinci daripada ibu bapa akan membantu pengasuh mempersiapkan dengan lebih baik.", "LABEL_NOTE_FOR_TASKER": "<PERSON>a untuk <PERSON>", "TASK_NOTE_DESCRIPTION": "Nota ini akan membantu Tasker bekerja dengan cepat dan baik.", "TITLE_CHECKBOX_TASK_NOTE": "<PERSON><PERSON><PERSON> untuk kerja yang dihantar kem<PERSON>an.", "DISINFECTION_SERVICE_NOTE_CONTENT": "<PERSON><PERSON> anda mempunyai permin<PERSON><PERSON> tambahan, sila masukkan di sini", "SUPPLY_DEMAND_COST_INCREASE": "<PERSON>rga meningkat kerana permintaan kerja yang tinggi pada masa ini.", "WEEKLY_REPEATER_IT_MEAN_TITLE": "Apa itu <PERSON>lang Mingguan?", "CANCEL": "<PERSON><PERSON>", "OK": "<PERSON><PERSON><PERSON>", "UNDERSTOOD": "<PERSON><PERSON><PERSON>", "POST_TASK_CHECKBOX_REPEAT": "<PERSON><PERSON><PERSON> setiap minggu", "COUNTDOWN_DAY": "<PERSON>", "COUNTDOWN_HOUR": "Jam", "TERMS_AND_CONDITIONS_TH": "⚠️ Terma dan <PERSON>", "TERMS_AND_CONDITIONS_1_TH": "1. <PERSON><PERSON> kes me<PERSON><PERSON>an kebenaran daripada pengurus kondominium atau wakil di kawasan tempat tinggal anda, sila sahkan agar Tasker bTaskee dapat melaksanakan kerja selepas 4:00 petang pada hari bekerja, hujung minggu atau hari cuti.", "TERMS_AND_CONDITIONS_2_TH": "2. <PERSON><PERSON> b<PERSON><PERSON><PERSON> tidak dapat melaksanakan kerja kerana orang yang diberi kuasa tidak membenarkan masuk ke kawasan kerja, anda akan dikenakan 100% daripada nilai kerja yang ditempah.", "PRE_BOOKING_WARNING_BOOKING_DATE": "{{EventName}} <PERSON><PERSON> ini membolehkan anda men<PERSON>an perkhidmatan dari {{startDate}} ke {{endDate}}. Untuk tempahan di luar tempoh ini atau permintaan segera (dalam masa 72 jam), sila gunakan pilihan tempahan biasa untuk sokongan terbaik (tersedia sehingga 7 hari lebih awal).", "CHOOSE_TIME": "<PERSON><PERSON><PERSON> masa kerja", "DIALOG_TITLE_INFORMATION": "Maklumat", "TOMORROW": "Esok", "PT2_POPUP_ERROR_TIME_CLOSE": "<PERSON><PERSON><PERSON>", "POSTTASK_STEP2_ERROR_TIME": "Masa tidak sah. <PERSON>la hantar sekurang-kurangnya {{t}} minit lebih awal.", "PT2_POPUP_ERROR_TIME_INVALID_CONTENT": "<PERSON>la pilih masa kerja lain. bTaskee hanya menyokong kerja dari {{from}} hingga {{to}} setiap hari.", "PT2_POPUP_ERROR_TIME_INVALID_CLOSE": "<PERSON><PERSON><PERSON>", "WEEKLY_REPEATER_BODY_1": "Ciri ini diperuntukkan bagi <PERSON>gan yang me<PERSON>lukan perkhidmatan **secara mingguan**.", "WEEKLY_REPEATER_BODY_2": "Sistem akan **secara automatik mendaftar pek<PERSON>** pada hari-hari dalam minggu yang telah anda pilih sebelum ini.", "WEEKLY_REPEATER_BODY_3": "<PERSON>a boleh secara proaktif mengubah masa kerja bagi pekerjaan yang telah didaft<PERSON> (di bahagian Menunggu Kerja); atau boleh mengemas kini maklumat Jadual Ulang terus di aplikasi.", "FAV_TASKER": {"TASKER": "Tasker", "SCHEDULE_OF_MORNING": "<PERSON><PERSON>", "SCHEDULE_OF_AFTERNOON": "<PERSON><PERSON>", "SCHEDULE_OF_EVENING": "Malam", "CONFLICT_TIME_NOTE": "Maaf! Waktu tersebut bertindih dengan jadual kerja Tasker. Sila lihat jadual kerja Tasker di bawah dan pilih waktu lain.", "OPTION_TIME": "<PERSON><PERSON><PERSON> {{stt}}", "ADD_OPTION_TIME": "Tambah waktu kerja", "ADD_OPTION_TIME_1": "Tambah waktu", "ADD_OPTION_TIME_2": "Tidak, terima kasih", "ADD_OPTION_TIME_CONTENT": "<PERSON>a boleh mencadangkan tambahan maksimum 03 waktu lain, supaya Tasker boleh menerima kerja dengan fleksibel. Aplikasi akan memaparkan waktu lapang Tasker untuk memudahkan pilihan anda.", "VOUCHER_DISCOUNT": "<PERSON><PERSON><PERSON> diskaun", "CHANGE_OPTION_TIME": "<PERSON><PERSON> waktu", "WARING_FAV_TASKER_NOT_ACCEPT": "Sekiranya Tasker k<PERSON><PERSON>n belum men<PERSON>, b<PERSON><PERSON><PERSON> men<PERSON> anda menghantar kerja kepada Tasker lain dalam sistem.", "SEND_TO_OTHER_TASKER": "<PERSON><PERSON> kerja kepada Tasker lain", "DESCRIPTION_SEND_TO_OTHER_TASKER": "<PERSON>la pilih waktu kerja yang sesuai untuk anda!", "FIND_OTHER_TASKER": "<PERSON><PERSON> lain", "CONTENT_CONFIRMED_FIND_OTHER_TASKER": "<PERSON>a men<PERSON>kan ingin menghantar kerja ini ke sistem untuk mencari Tasker lain.", "WORKING_TIME": "<PERSON><PERSON><PERSON> kerja", "WORKING_DATE": "<PERSON><PERSON><PERSON> k<PERSON>ja", "PRICE": "<PERSON><PERSON>", "WARNING_PAYMENT_METHOD_CASH": "Selepas Tasker men<PERSON><PERSON>, sistem akan mengemas kini harga perkhidmatan mengikut Waktu yang dipilih oleh Tasker.", "WARNING_PAYMENT_METHOD_CARD": "<PERSON>rga perkhidmatan akan dikemas kini mengikut Waktu yang dipilih oleh Tasker. Sistem hanya akan memotong wang selepas Tasker menerima kerja.", "WARNING_PAYMENT_METHOD_PREPAID": "Untuk menempah kerja ini, anda perlu membayar jumlah maksimum dalam Waktu yang ada. Jika Tasker memilih waktu dengan harga yang lebih rendah, perbezaan jumlah akan dikembalikan ke dompet elektronik yang sesuai.", "WAITING_TASKER_ACCEPT": "<PERSON>dang menunggu {{tasker}} men<PERSON>ma kerja...", "WARNING_CHANGE_TASK_1": "<PERSON>a tidak boleh mengubah waktu kerja kerana kerja telah dihantar kepada Tasker kegemaran. <PERSON><PERSON> t<PERSON>, Tasker akan memberi mak<PERSON> balas kepada anda secepat mungkin.", "WARNING_CHANGE_TASK_2": "<PERSON>ka ingin mengubah waktu kerja, sila berborak dengan Tasker sebelum melakukan tindakan Batalkan kerja.", "CANCEL_TASK": "Batalkan kerja", "SEE_TASKER_SCHEDULE": "<PERSON><PERSON> j<PERSON><PERSON> kerja <PERSON>", "TASKER_WORKING_SCHEDULE": "<PERSON><PERSON><PERSON> ker<PERSON>", "TASKER_NOT_SUPPORT_BOOKING": "<PERSON><PERSON>, anda tidak boleh menggunakan fungsi tempahan semula dengan Tasker kegemaran dengan Tasker ini. Kerana perkhidmatan yang ditawarkan oleh Tasker tidak menyokong fungsi ini.", "WARNING_BOOKING_WITH_FAV_TASKER": "Anda perlu membuat tempahan sekurang-kurangnya 6 jam lebih awal supaya Tasker Kegemaran mempunyai masa yang cukup untuk menerima kerja. <PERSON>la pilih waktu lain.", "WARNING_PAYMENT_METHOD_KREDIVO": "<PERSON><PERSON> đặt công vi<PERSON><PERSON>, bạn cần thanh toán số tiền cao nhất trong các <PERSON>hung <PERSON>ờ. Nếu Tasker chọn khung giờ có gi<PERSON> thấp h<PERSON>, số tiền chênh lệch sẽ được hoàn về ví bPay của bạn.", "TASKER_WILL_ARRIVE_AT_TIME": "Tasker akan tiba dalam tempoh {{time}}", "ADD_OPTION": "Tambah lebih banyak pilihan", "ADD_OPTION_NOTE": "Sekiranya Tasker menolak atau tidak respons terhadap tugas ini dalam masa yang ditetapkan, adakah anda mahu sistem itu menghantar tugas kepada Tasker yang lain?", "ADD_OPTION_NOTE_2": "<PERSON><PERSON> anda tidak memilih pilihan ini, kami akan member<PERSON><PERSON> anda sekiranya Tasker menolak atau tidak bertindak balas.", "ADD_OPTION_CHECK_BOOK": "Hantarkan tugas ini kepada Tasker yang lain", "TIME_FRAME": "<PERSON><PERSON><PERSON>"}, "DIALOG": {"ERROR_TRY_AGAIN": "<PERSON><PERSON><PERSON><PERSON> ralat, sila cuba lagi [{{t}}]", "USERNAME_PASSWORD_INCORRECT": "Nombor telefon atau kata laluan tidak betul. Sila cuba lagi!", "ERROR_TIME_INVALID_CONTENT": "<PERSON>la pilih waktu kerja yang lain. bTaskee hanya menyokong kerja dari {{from}} hingga {{to}}."}, "CHAT_FAV": {"MISSED_CALL": "Panggilan tidak dijawab", "INCOME_CALL": "Panggilan masuk", "OUTGOING_CALL": "Panggilan keluar", "CALL_AGAIN": "Klik untuk menelefon kembali", "WAITING_TASKER_ACCEPT": "Menunggu Tasker men<PERSON>", "SENT_FOR_TASKER": "Sistem telah menghantar pemberitahuan kepada Tasker", "HOUR": "jam", "MINUTE": "minit", "SECOND": "saat", "WAITING_ACCEPT": "<PERSON><PERSON><PERSON> pen<PERSON>", "COMING_SOON": "<PERSON><PERSON><PERSON><PERSON>", "CONFIRM_CANCEL": "<PERSON><PERSON><PERSON> anda mahu membatalkan tugas ini?", "TASK_EXPIRED_AFTER": "<PERSON><PERSON> akan tamat tempoh", "CHOOSE_DATE_OPTION": "<PERSON><PERSON><PERSON> masa kerja", "CHOOSE_DATE_OPTION_CONTENT": "<PERSON>la pilih masa kerja sebelum menghantar tugas kepada Tasker yang lain", "SENT_TO_OTHER_TASKER": "<PERSON><PERSON> ke Tasker lain", "SENT_TO_OTHER_TASKER_CONTENT": "<PERSON><PERSON><PERSON> anda ingin menghantar tugas kepada Tasker lain?", "BOOK_TASK": "<PERSON><PERSON><PERSON> servis", "CONFIRM_CHANGE_DATE_OPTION": "<PERSON><PERSON> ke masa lain bekerja", "CONTENT_CONFIRM_CHANGE_DATE_OPTION": "<PERSON><PERSON><PERSON> anda pasti mahu menukar masa kerja ke slot masa yang dicadangkan oleh Tasker?", "REJECT_CHANGE_DATE_OPTION": "Menolak permintaan untuk menukar masa kerja", "CONTENT_REJECT_CHANGE_DATE_OPTION": "Tugas akan dihantar kembali ke Tasker dengan slot masa yang anda pilih. <PERSON><PERSON><PERSON> anda pasti mahu menolak slot masa yang dicadangkan oleh Tasker?", "SENDING": "Menghantar", "CONTENT_TO_FAV_TASKER": "Berhubungi Tasker kegemaran anda dengan cepat untuk berbual sebelum meletakkan tugas!", "CHAT_WITH_FAV": "Berbual dengan tasker k<PERSON><PERSON><PERSON> anda", "WARNING_TIME_BOOK_TASK": "Untuk memastikan Tasker kegemaran anda dapat menerima tugas itu, sila bincangkan waktu kerja dengan mereka sebelum membuat tempahan."}, "BOOK_TASK_AND_PAYMENT": "Pembayaran", "BUTTON_BOOKING": "<PERSON><PERSON> kerja", "TET_BOOKING_TO_NORMAL_TASK": "<PERSON><PERSON> kepada tugas biasa", "TET_BOOKING_TO_NORMAL_TASK_DESCRIPTION": "<PERSON>lik di sini jika anda tidak mahu membuat bayaran lebih awal dan ingin kembali kepada tugas biasa (Bayaran tunai, tempahan maksimum 7 hari sebelum)", "TET_BOOKING_TO_NOMAL_NOTE_TITLE": "<PERSON>a", "TET_BOOKING_TO_NOMAL_NOTE_DONE": "<PERSON><PERSON> kini", "TET_BOOKING_TO_NORMAL_TASK_CHANGE_DATE": "<PERSON><PERSON><PERSON> kerja yang dipilih tidak sesuai. <PERSON>a perlu mengemas kini semula masa kerja yang sesuai untuk tugas biasa - Ma<PERSON><PERSON><PERSON> dalam 7 hari akan datang.", "STEP_4_UPDATE_CALENDAR_TITLE": "<PERSON><PERSON><PERSON>", "STEP_4_UPDATE_TIME_TITLE": "<PERSON><PERSON><PERSON> masa bekerja", "TOTAL": "<PERSON><PERSON><PERSON>", "LOCATION": "Lokasi kerja", "HK_SV_ADDRESS_DETAIL": "Detail alamat", "EDIT": "Ubah", "CONTACT_INFO_MODAL_TITLE": "Maklumat hubungan", "PHONE_NUMBER": "Nombor telefon", "CONTACT_NAME": "<PERSON><PERSON>", "PRICE_SERVICE": "<PERSON><PERSON>", "TOTAL_PAYMENT": "<PERSON><PERSON><PERSON>", "TITLE_PAYMENT_DETAIL": "<PERSON><PERSON><PERSON>", "TAB_PROMOTION": "<PERSON><PERSON><PERSON>", "PAYMENT_METHOD": "<PERSON><PERSON><PERSON>", "WORKLOAD": "<PERSON><PERSON> kerja", "HAVE_PET": "<PERSON><PERSON><PERSON> ada haiwan peli<PERSON>an", "ADD_ON_SERVICE": "Perkhidmatan tambahan", "IS_PREMIUM": "<PERSON><PERSON>", "TASK_PREMIUM": "Perkhidmatan Premium", "TASK_INFO": "Maklumat kerja", "WORK_IN": "<PERSON><PERSON><PERSON>", "WORKING_DAY": "<PERSON> kerja", "TIME_TO_WORK": "<PERSON><PERSON>", "TERMS_OF_USED_SERVICES_1_TH": "1. <PERSON><PERSON> anda telah membuat pembayaran melalui PromptPay tetapi kerja dibatalkan kerana tiada penerima, wang akan dikembalikan sepenuhnya ke dalam akaun bPay anda.", "TERMS_OF_USED_SERVICES_2_TH": "2. <PERSON><PERSON> anda ingin mengeluarkan wang dari akaun b<PERSON><PERSON> anda, anda akan dikenakan caj pengeluaran sebanyak 20% dari jumlah yang dikeluarkan.", "TERMS_OF_USED_SERVICES_3_TH": "3. <PERSON><PERSON> akan <PERSON> bayaran balik 100% jika pengeluaran dari b<PERSON><PERSON> dilakukan dalam situasi berikut:", "TERMS_OF_USED_SERVICES_3_1_TH": "3.1 Telah membayar melalui PromptPay tetapi perkhidmatan dibatalkan kerana tiada penerima kerja.", "TERMS_OF_USED_SERVICES_3_2_TH": "3.2 Tasker at<PERSON> rakan k<PERSON> bTaskee membatalkan kerja kurang dari 2 jam sebelum waktu kerja", "TERMS_OF_USED_SERVICES_4_TH": "4. <PERSON><PERSON><PERSON><PERSON> <PERSON>ri akaun b<PERSON>ay akan mengambil masa antara 7 hingga 14 hari bekerja untuk diproses.", "TERMS_OF_USED_SERVICES_5_TH": "5. In<PERSON>is <PERSON>khidmatan akan dikeluarkan berdasarkan yuran platform bTaskee dan termasuk <PERSON> (VAT).", "TERMS_OF_USED_SERVICES_TH": "<PERSON>a", "TERM_OF_PAYMENT_METHOD_E_WALL": {"TERM_1": "1. <PERSON><PERSON> anda me<PERSON>, baki yang <PERSON>, set<PERSON>h di<PERSON>lak yuran <PERSON>, akan dikembalikan ke e-Wallet anda.", "TERM_2": "2. <PERSON><PERSON> layak untuk mendapatkan pengembalian 100% hanya jika pembatalan dibuat oleh Tasker.", "TERM_3": "3. <PERSON><PERSON><PERSON><PERSON> akan diproses dalam masa 7-14 hari be<PERSON><PERSON>."}, "TERM_OF_PAYMENT_METHOD_CARD": {"TERM_1": "Kad anda akan dikenakan bayaran selepas tugas telah disahkan.", "TERM_2": "Pembatalan dan <PERSON>", "TERM_2_1": "1. <PERSON><PERSON> anda me<PERSON>, baki yang tin<PERSON>, set<PERSON>h di<PERSON>lak yuran <PERSON>, akan dike<PERSON>an ke kad anda.", "TERM_2_2": "2. <PERSON><PERSON> layak untuk mendapatkan pengembalian 100% hanya jika pembatalan dibuat oleh Tasker.", "TERM_2_3": "3. <PERSON><PERSON><PERSON><PERSON> akan diproses dalam masa 7-14 hari be<PERSON><PERSON>."}, "LIMIT_DURATION": "Masa kerja maksimum ialah {{t}}h. <PERSON><PERSON> kurangkan masa kerja untuk menambah perkhidmatan tambahan", "WORK_TIME_TITLE": "<PERSON><PERSON><PERSON> masa kerja", "PT2_CONFIRM_HEADER_TITLE": "<PERSON><PERSON><PERSON> dan bayar", "WORK_IN_TIME_FROM_A_TO_B": "{{t1}} jam, dari {{t2}} hingga {{t3}}", "DOG": "<PERSON><PERSON><PERSON>", "CAT": "Kucing", "PAYMENT_METHOD_CARD": "Kad Visa/Master", "PAYMENT_METHOD_BANK_TRANSFER": "Kad ATM/Internet Banking", "PAYMENT_METHOD_CREDIT": "b<PERSON>ay", "PAYMENT_METHOD_MOMO": "MoMo", "PAYMENT_METHOD_DIRECT_TRANSFER": "<PERSON><PERSON><PERSON> terus", "PAYMENT_METHOD_DIRECT_CASH": "Tunai", "PAYMENT_METHOD_ZALO_PAY": "Zalopay", "TASK_SAME_TIME_MESSAGE": "Anda sudah mempunyai satu kerja pada waktu ini, adakah anda ingin menambah satu lagi Tasker?", "PAYMENT_METHOD_VIRTUAL_ACCOUNT": "<PERSON><PERSON><PERSON> aka<PERSON> maya", "MODAL_POST_TASK_SUCCESS_TITLE": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "MODAL_POST_TASK_SUCCESS_CONTENT": "Anda telah mendaftar kerja dengan berjay<PERSON>, anda boleh menyemak kerja di halaman aktiviti", "MODAL_POST_TASK_SUCCESS_BTN_FOLLOW_TASK": "<PERSON><PERSON><PERSON> kerja", "MODAL_POST_TASK_SUCCESS_BTN_GO_HOME": "<PERSON><PERSON><PERSON> ke halaman utama", "HIDE_PREMIUM_TOOLS_DETAIL": "Sembunyikan", "SEE_MORE": "<PERSON><PERSON> le<PERSON>h la<PERSON>"}