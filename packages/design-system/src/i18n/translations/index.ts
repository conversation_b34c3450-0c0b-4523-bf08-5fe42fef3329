const enTranslations = {
  deepCleaning: require('./en/deep-cleaning.json'),
  common: require('./en/common.json'),
  host: require('./en/host.json'),
  officeCleaning: require('./en/office-cleaning.json'),
  cleaningSub: require('./en/cleaning-subscription.json'),
  payment: require('./en/payment.json'),
  cleaning: require('./en/cleaning.json'),
  voiceChat: require('./en/voice-chat.json'),
};

const koTranslations = {
  deepCleaning: require('./ko/deep-cleaning.json'),
  common: require('./ko/common.json'),
  host: require('./ko/host.json'),
  officeCleaning: require('./ko/office-cleaning.json'),
  cleaningSub: require('./ko/cleaning-subscription.json'),
  payment: require('./ko/payment.json'),
  cleaning: require('./ko/cleaning.json'),
  voiceChat: require('./ko/voice-chat.json'),
};

const idTranslations = {
  deepCleaning: require('./id/deep-cleaning.json'),
  common: require('./id/common.json'),
  host: require('./id/host.json'),
  officeCleaning: require('./id/office-cleaning.json'),
  cleaningSub: require('./id/cleaning-subscription.json'),
  payment: require('./id/payment.json'),
  cleaning: require('./id/cleaning.json'),
  voiceChat: require('./id/voice-chat.json'),
};

const msTranslations = {
  deepCleaning: require('./ms/deep-cleaning.json'),
  common: require('./ms/common.json'),
  host: require('./ms/host.json'),
  officeCleaning: require('./ms/office-cleaning.json'),
  cleaningSub: require('./ms/cleaning-subscription.json'),
  payment: require('./ms/payment.json'),
  cleaning: require('./ms/cleaning.json'),
  voiceChat: require('./ms/voice-chat.json'),
};

const thTranslations = {
  deepCleaning: require('./th/deep-cleaning.json'),
  common: require('./th/common.json'),
  host: require('./th/host.json'),
  officeCleaning: require('./th/office-cleaning.json'),
  cleaningSub: require('./th/cleaning-subscription.json'),
  payment: require('./th/payment.json'),
  cleaning: require('./th/cleaning.json'),
  voiceChat: require('./th/voice-chat.json'),
};

const viTranslations = {
  deepCleaning: require('./vi/deep-cleaning.json'),
  common: require('./vi/common.json'),
  host: require('./vi/host.json'),
  officeCleaning: require('./vi/office-cleaning.json'),
  cleaningSub: require('./vi/cleaning-subscription.json'),
  payment: require('./vi/payment.json'),
  cleaning: require('./vi/cleaning.json'),
  voiceChat: require('./vi/voice-chat.json'),
};

export const translations = {
  en: enTranslations,
  ko: koTranslations,
  id: idTranslations,
  ms: msTranslations,
  th: thTranslations,
  vi: viTranslations,
};
