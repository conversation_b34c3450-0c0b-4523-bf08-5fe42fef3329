{"SV_HC_SCR2_DETAIL_BTN_COST_CURRENCY": "{{cost}} {{currency}}", "SV_HC_SCR2_DETAIL_EXTRA_SV_TITLE": "Add-on service", "SV_HC_SCR2_DETAIL_EXTRA_SV_NOTE": "You can choose to add services.", "BTASKEE": "b<PERSON><PERSON><PERSON>", "POST_TASK_STEP_2": {"LIST_TOOL_TASK_NORMAL": "The service includes standard cleaning supplies", "LIST_TOOL_TASK_PREMIUM": "The service includes standard cleaning supplies and a handheld vacuum cleaner.", "TITLE_MODAL_LIST_TOOLS": "Tools, equipment, and chemicals", "PREMIUM": "Premium", "OPTION_PET_FEE": "To clean the pet area effectively, Taskers need to be equipped with special tools and chemicals. Therefore, when selecting this option, an additional fee of {{price}} will be applied.", "OPTION_PET_NOTE": "Some Taskers are allergic to pet hair and cannot perform the task. Please specify the type of pet for the best support.", "SOME_RECOMMEND": "Some suggestions for you:", "RECOMMEND_1": "- Some Taskers are allergic to pet hair and cannot perform the task. Please specify the type of pet for the best support.", "RECOMMEND_2": "- To ensure the safety of both Tasker and your pets, please keep your pets in a cage or a separate area while the Tasker is working.", "AIR_CONDITION_TITLE": "AC Cleaning ({{from}} - {{to}}{{unit}})"}, "CLOSE": "Close", "MESSAGE_NAME_INVALID": "Names must not contain numbers and special characters", "MESSAGE_EMAIL_INVALID": "Invalid email", "PHONE_NUMBER_SYNTAX_IS_INCORRECT": "Phone number is incorrect", "ACCOUNT_ACTIVATION_ERROR_PASSWORD_LENGTH": "Password 6 - 12 characters, no spaces.", "THIS_FIELD_IS_REQUIRED": "Required Information", "MONEY_SYNTAX_INVALID": "Invalid amount", "NUMBER_OF_HOURS": "{{t}} hours", "DURATION_TEXT": "Maximum {{t1}} or {{t2}}", "CONTENT_NOT_SUPPORT_CITY": "Service is not yet available in this city.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3_TH": "Two hour before the task starts, if you don't choose, the system will automatically choose a tasker and the fee of add-on service will still be charged.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_3": "One hour before the task starts, if you don't choose, the system will automatically choose a tasker and the fee of add-on service will still be charged.", "PT1_DETAIL_CHOOSE_MANUAL_FEES": "Fees", "PT1_DETAIL_CHOOSE_MANUAL_COST_AND_CURRENCY": "{{cost}} {{currency}}", "PT1_DETAIL_OPTION_ITEM_CHOOSE_MANUAL": "Manually choose Tasker", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_1": "This function allows multiple Taskers to accept your task. You can choose one of these Taskers to clean your house.", "PT1_DETAIL_CHOOSE_TASKER_EXPLAIN_2": "You need to book at least **{{hour}} hours** in advance to use this feature.", "PT1_DETAIL_CHOOSE_MANUAL_UNDERSTOOD": "I understood", "FAV_TASKER_TITLE": "What is \"Prioritize favorite Taskers\" ?", "FAV_TASKER_DESCRIPTION_EXPLAIN_1": "This is the default function when you have a list of favorite Taskers.", "FAV_TASKER_DESCRIPTION_EXPLAIN_2": "Your favorite Taskers will be prioritized. If a favorite Tasker is busy or not taking the task, the system will send your task to Taskers nearby.", "BOOKING_STEP_2": {"CHOOSE_GENDER": "<PERSON><PERSON> Tasker Gender", "GENDER_MALE": "Male", "GENDER_FEMALE": "Female", "WHAT_IS_CHOOSE_GENDER": "What is the Choose Tasker Gender?", "DESCRIPTION_CHOOSE_GENDER": "Depending on your specific needs, you can flexibly choose the gender of the tasker to ensure the quality of work and have more convenience and comfort when you experience the service."}, "PT1_DETAIL_OPTION_TITLE": "Options", "PT1_MAP_POPUP_ADD_PET_SUBMIT": "Ok", "PT1_DETAIL_OPTION_HAVE_PET": "House with pets", "COST_AND_CURRENCY": "{{cost}} {{currency}}", "OTHER": "Others", "PREMIUM_TITLE_OPTIONAL": "Choose Premium Service", "PREMIUM_CONTENT_OPTIONAL": "Premium Service", "PREMIUM_DETAIL_2": "Performed by premium Taskers who have received advanced training and a Premium license from bTaskee.", "PREMIUM_DETAIL_3": "Standard Cleaning Tools: A handheld vacuum cleaner is included.", "PREMIUM": "[object Object]", "PREMIUM_DETAIL_1": "What does Premium Service entail?", "NEXT": "Next", "UPDATE": "Update", "INCLUDE_FEE_SHIP": "Shipping included", "LAUNDRY_EMERGENCY_FEE": "{{t1}} {{t2}} (same day drop-off fee)", "TASK_DETAIL": "Task details", "ADD_ADDRESS": "Add a new address", "SIGN_UP_NOW": "Sign in now", "LIST_OF_LOCATIONS": "List of places", "SV_HC_SCR2_DETAIL_DURATION_TITLE_NOTE_TH": "Please estimate the area and time needed for cleaning. **For moving-in/out tasks**, switch to the **Big Cleaning** option, as it is more suitable for the task load. Cleaning Service (on-demand) is for regular cleaning only.", "SV_HC_SCR2_DETAIL_DURATION_TITLE_NOTE": "Please estimate the exact area for cleaning", "SV_HC_SCR2_DETAIL_NOTE_INDO": "*Note: This service is intended for routine daily cleaning and does not cover specific needs such as post-renovation or post-flood cleaning.", "SV_HC_SCR2_DETAIL_NOTE": "*Note: we only service up to 4h. If you need more than 4h, please book ", "NOTE_POST_TASK_CLEANING_1": "Deep cleaning service", "NOTE_POST_TASK_CLEANING_2": " or 2 separate sessions.", "SV_HC_SCR2_DETAIL_DURATION_TITLE": "Duration", "NUMBER_OF_ROOMS": "{{t}} rooms", "PT1_DETAIL_OPTION_ITEM_FAV_TASKER": "Prioritize favorite Taskers", "FAVORITE": "Favourite", "POST_TASK_BEFORE_HOUR": "You need to book at least **{{hour}} hours** in advance to use the **Manually choose Tasker** feature. Please choose a different time slot.", "WORK_TIME": "Working time", "CHOOSE_DATE": "Date", "ELDERLY_CARE_CHOOSE_TIME_TITLE": "Choose the Timing", "WORKING_SCHEDULE": "Working schedule", "CONFIRM": "Confirm", "CHILD_CARE_NOTE_TITLE": "Notes for tasker", "TASK_NOTE_DESCRIPTION_CHILD_CARE": "Detailed notes of parent will help the tasker to prepare better.", "LABEL_NOTE_FOR_TASKER": "Notes for Tasker", "TASK_NOTE_DESCRIPTION": "This note will help Task<PERSON> do a better job", "TITLE_CHECKBOX_TASK_NOTE": "Use for next booking", "DISINFECTION_SERVICE_NOTE_CONTENT": "If you have any further requests, please enter them here", "SUPPLY_DEMAND_COST_INCREASE": "The price is increased due to high demand at this time.", "WEEKLY_REPEATER_IT_MEAN_TITLE": "What is Weekly Schedule?", "CANCEL": "Cancel", "OK": "Agree", "UNDERSTOOD": "I understood", "POST_TASK_CHECKBOX_REPEAT": "Weekly Schedule", "COUNTDOWN_DAY": "Days", "COUNTDOWN_HOUR": "Hours", "TERMS_AND_CONDITIONS_TH": "⚠️ T&C", "TERMS_AND_CONDITIONS_1_TH": "1. Please confirm that the juristic person of your village or condominium will permit the cleaner or the technician to access the area to perform a service before making a reservation after 4:00 PM on business days, Saturday through Sunday, or on public holidays.", "TERMS_AND_CONDITIONS_2_TH": "2. If the technician or the cleaner is unable to provide service because the juristic person is not allowed to enter the area, you must be charged 100% of the service fee to the company.", "PRE_BOOKING_WARNING_BOOKING_DATE": "{{eventName}} feature allows you to schedule services from {{startDate}} to {{endDate}}. For bookings outside this period or urgent requests (within 72 hours), please use the Regular Booking option for the best support (available up to 7 days in advance).", "CHOOSE_TIME": "Time", "DIALOG_TITLE_INFORMATION": "Notification", "TOMORROW": "Tomorrow", "YESTERDAY": "Yesterday", "PT2_POPUP_ERROR_TIME_CLOSE": "Close", "POSTTASK_STEP2_ERROR_TIME": "Time invalid. Please book at least {{t}} minutes in advance.", "PT2_POPUP_ERROR_TIME_INVALID_CONTENT": "Please choose different working time. We only allow working time from {{from}} to {{to}}.", "PT2_POPUP_ERROR_TIME_INVALID_CLOSE": "Close", "WEEKLY_REPEATER_BODY_1": "This option is used for customers who have **weekly service use** demand.", "WEEKLY_REPEATER_BODY_2": "This option is used for customers who want to **use service every week**.", "WEEKLY_REPEATER_BODY_3": "You can proactively change the working time of posted tasks (in the Waiting); or you can update the information of the Weekly Schedule right on the app.", "FAV_TASKER": {"TASKER": "Tasker", "SCHEDULE_OF_MORNING": "Morning", "SCHEDULE_OF_AFTERNOON": "Afternoon", "SCHEDULE_OF_EVENING": "Evening", "CONFLICT_TIME_NOTE": "Sorry! The time slot you selected conflicts with the Tasker's schedule. Please check the Tasker's schedule below and choose a different time slot.", "OPTION_TIME": "Option {{stt}}", "ADD_OPTION_TIME": "Add working time slot", "ADD_OPTION_TIME_1": "Add option", "ADD_OPTION_TIME_2": "No, thanks", "ADD_OPTION_TIME_CONTENT": "You can also suggest up to 3 additional time slots for Taskers to flexibly choose according to their schedules. The app will display Taskers' available time slots to help you easily make selections.", "VOUCHER_DISCOUNT": "Voucher giảm giá", "CHANGE_OPTION_TIME": "Change working time", "WARING_FAV_TASKER_NOT_ACCEPT": "In case your favorite Tasker has not yet been confirmed, b<PERSON><PERSON><PERSON> encourages you to resend the task to other Taskers on the system.", "SEND_TO_OTHER_TASKER": "Resend the task to other Taskers", "DESCRIPTION_SEND_TO_OTHER_TASKER": "Please select the time slots that suits you!", "FIND_OTHER_TASKER": "Find other Taskers", "CONTENT_CONFIRMED_FIND_OTHER_TASKER": "You confirm that you want to resend this task to the system to find other Taskers.", "WORKING_TIME": "Working time", "WORKING_DATE": "Date", "PRICE": "Price", "WARNING_PAYMENT_METHOD_CASH": "After Task<PERSON> accepts the task, the system will update the service price according to the option that <PERSON><PERSON> has chosen.", "WARNING_PAYMENT_METHOD_CARD": "The service price will be updated according to the option that Tasker has selected. The system will only deduct money after Tasker accepts the task.", "WARNING_PAYMENT_METHOD_PREPAID": "To book this task, you need to pay the highest amount within the option. If Tasker chooses a option with a lower price, the difference will be refunded to the corresponding e-wallet.", "WAITING_TASKER_ACCEPT": "The task is awaiting for {{tasker}} to accept...", "WARNING_CHANGE_TASK_1": "You can't change the working time because the task has been sent to a favorite Tasker. Please wait, the Tasker will respond to you as soon as possible.", "WARNING_CHANGE_TASK_2": "If you want to change the working time, please chat with the Tasker before canceling the task.", "CANCEL_TASK": "Cancel", "SEE_TASKER_SCHEDULE": "View Tasker's schedule", "TASKER_WORKING_SCHEDULE": "Tasker's schedule", "TASKER_NOT_SUPPORT_BOOKING": "Sorry, you can't use the rebooking feature with your favorite Tasker for this Tasker. Due to the services Tasker is currently providing do not support this feature.", "WARNING_BOOKING_WITH_FAV_TASKER": "You need to book at least 3 hours in advance so that your Favorite Tasker has enough time to accept the task. Please select a different working time.", "WARNING_PAYMENT_METHOD_KREDIVO": "To book this task, you need to pay the highest amount within the available time slots. If the Tasker chooses a time slot with a lower price, the difference will be refunded to your bPay wallet.", "TASKER_WILL_ARRIVE_AT_TIME": "Tasker will arrive at {{time}}", "ADD_OPTION": "Add more options", "ADD_OPTION_NOTE": "If the Tasker declines or does not respond to this task within the specified time, would you like the system to resend task to another Tasker?", "ADD_OPTION_NOTE_2": "If you do not choose this option, we will notify you when the Tasker declines or does not respond.", "ADD_OPTION_CHECK_BOOK": "Send this task to another Tasker", "TIME_FRAME": "Option"}, "DIALOG": {"ERROR_TRY_AGAIN": "There was a error. Please try again[{{t}}]", "USERNAME_PASSWORD_INCORRECT": "The phone number or password is incorrect. Please try again!", "ERROR_TIME_INVALID_CONTENT": "Please choose different working time. We only allow working time from {{from}} to {{to}}."}, "CHAT_FAV": {"MISSED_CALL": "Missed Call", "INCOME_CALL": "Incoming Call", "OUTGOING_CALL": "Outgoing Call", "CALL_AGAIN": "Click to Call Back", "WAITING_TASKER_ACCEPT": "Waiting for Tasker to confirm", "SENT_FOR_TASKER": "The system has sent a notification to Tasker", "HOUR": {"one": "{{count}} hour", "other": "{{count}} hours"}, "MINUTE": {"one": "{{count}} minute", "other": "{{count}} minutes"}, "SECOND": {"one": "{{count}} second", "other": "{{count}} seconds"}, "WAITING_ACCEPT": "Waiting for confirmation", "COMING_SOON": "Comfirmed", "CONFIRM_CANCEL": "Do you want to cancel this task?", "TASK_EXPIRED_AFTER": "The task will expire in ", "CHOOSE_DATE_OPTION": "Select working time", "CHOOSE_DATE_OPTION_CONTENT": "Please select a working time before sending the task to another Tasker", "SENT_TO_OTHER_TASKER": "Send to another Tasker", "SENT_TO_OTHER_TASKER_CONTENT": "Do you want to send the task to another Tasker?", "BOOK_TASK": "Book service", "CONFIRM_CHANGE_DATE_OPTION": "Change to another working time", "CONTENT_CONFIRM_CHANGE_DATE_OPTION": "Are you sure you want to change the working time to the time slot proposed by the Tasker?", "REJECT_CHANGE_DATE_OPTION": "Reject request to change the working time", "CONTENT_REJECT_CHANGE_DATE_OPTION": "The task will be sent back to the Tasker with the time slot you selected. Are you sure you want to reject the time slot proposed by the Tasker?", "SENDING": "Sending", "CONTENT_TO_FAV_TASKER": "Connect with your favorite Tasker quickly to chat before placing the task!", "CHAT_WITH_FAV": "Chat with your favorite Tasker", "WARNING_TIME_BOOK_TASK": "To ensure your Favorite Tasker can accept the task, please discuss the working time with them before booking."}, "BOOK_TASK_AND_PAYMENT": "Payment", "BUTTON_BOOKING": "Book", "TET_BOOKING_TO_NORMAL_TASK": "Switch to regular booking", "TET_BOOKING_TO_NORMAL_TASK_DESCRIPTION": "Click here if you do not want to pay now and wish to return to  regular booking (Cash payment, bookings should be made up to 7 days in advance)", "TET_BOOKING_TO_NOMAL_NOTE_TITLE": "Note", "TET_BOOKING_TO_NOMAL_NOTE_DONE": "Update", "TET_BOOKING_TO_NORMAL_TASK_CHANGE_DATE": "The task date you have selected is not suitable. You need to update your task timing to fit regular booking - Withing the next 7 days.", "STEP_4_UPDATE_CALENDAR_TITLE": "Working Day", "STEP_4_UPDATE_TIME_TITLE": "Select work hours", "TOTAL": "Total", "LOCATION": "Location", "HK_SV_ADDRESS_DETAIL": "Address details", "EDIT": "Change", "CONTACT_INFO_MODAL_TITLE": "Contact", "PHONE_NUMBER": "Phone number", "CONTACT_NAME": "Contact name", "PRICE_SERVICE": "Service cost", "TOTAL_PAYMENT": "Total Payment", "TITLE_PAYMENT_DETAIL": "Payment Details", "TAB_PROMOTION": "Promotion", "PAYMENT_METHOD": "Payment method", "WORKLOAD": "Workload", "HAVE_PET": "House with pets", "ADD_ON_SERVICE": "Add-on service", "IS_PREMIUM": "Type of service", "TASK_PREMIUM": "Premium Service", "TASK_INFO": "Task Info", "WORK_IN": "Duration", "WORKING_DAY": "Date", "TIME_TO_WORK": "Working time", "TERMS_OF_USED_SERVICES_1_TH": "1. In case that customer paid by PromptPay but the task was canceled because there is no service provider, the money will be automatically refunded to bPay account with full amount.", "TERMS_OF_USED_SERVICES_2_TH": "2. If you want to withdraw money from your bPay account, there will be a withdrawal fee of 20% of the withdrawal amount.", "TERMS_OF_USED_SERVICES_3_TH": "3. Withdrawals from bPay accounts that are eligible for 100% refund are only in the conditions below:", "TERMS_OF_USED_SERVICES_3_1_TH": "3.1 Paid via Promptpay but canceled the service because there was no service provider longer than 6 hours.", "TERMS_OF_USED_SERVICES_3_2_TH": "3.2 The service provider cancels the task less than 2 hours before the service time.", "TERMS_OF_USED_SERVICES_4_TH": "4. Withdrawals from bPay accounts will take 7 - 14 business days to process.", "TERMS_OF_USED_SERVICES_5_TH": "5. The service invoice will be issued based on the bTaskee platform fee and includes value-added tax (VAT).", "TERMS_OF_USED_SERVICES_TH": "Note", "TERM_OF_PAYMENT_METHOD_E_WALL": {"TERM_1": "1. If you cancel the task, the remaining balance, after deducting the cancellation fee, will be refunded to your e-Wallet.", "TERM_2": "2. You are eligible for a 100% refund only if the cancellation is made by the Tasker.", "TERM_3": "3. Refunds will be processed within 7-14 business days.", "TERM_4": "4. The service invoice will be issued based on the bTaskee platform fee and includes value-added tax (VAT)."}, "TERM_OF_PAYMENT_METHOD_CARD": {"TERM_1": "Your card will be charged after the task has been confirmed.", "TERM_2": "Cancellations and Refunds", "TERM_2_1": "1. If you cancel the task, the remaining balance, after deducting the cancellation fee, will be refunded to your card.", "TERM_2_2": "2. You are eligible for a 100% refund only if the cancellation is made by the Tasker.", "TERM_2_3": "3. Refunds will be processed within 7-14 business days.", "TERM_3": "The service invoice will be issued based on the bTaskee platform fee and includes value-added tax (VAT)."}, "LIMIT_DURATION": "The max duration is {{t}}. Please reduce the time or book 2 consecutive tasks.", "WORK_TIME_TITLE": "Choose working time", "PT2_CONFIRM_HEADER_TITLE": "Confirm and pay", "WORK_IN_TIME_FROM_A_TO_B": "{{t1}} hours, from {{t2}} to {{t3}}", "DOG": "Dog", "CAT": "Cat", "PAYMENT_METHOD_CARD": "Visa/Master Card", "PAYMENT_METHOD_BANK_TRANSFER": "ATM / Internet banking", "PAYMENT_METHOD_CREDIT": "b<PERSON>ay", "PAYMENT_METHOD_MOMO": "<PERSON><PERSON>", "PAYMENT_METHOD_DIRECT_TRANSFER": "Bank transfer", "PAYMENT_METHOD_DIRECT_CASH": "Cash", "PAYMENT_METHOD_ZALO_PAY": "Zalopay", "TASK_SAME_TIME_MESSAGE": "You already book one cleaner at this time. Are you sure you want to book one more cleaner?", "PAYMENT_METHOD_VIRTUAL_ACCOUNT": "Virtual Account", "MODAL_POST_TASK_SUCCESS_TITLE": "Your task is successfully posted.", "MODAL_POST_TASK_SUCCESS_CONTENT": "You have successfully posted the Task. You can check for details on the Activity menu.", "MODAL_POST_TASK_SUCCESS_BTN_FOLLOW_TASK": "Task tracking", "MODAL_POST_TASK_SUCCESS_BTN_GO_HOME": "Back to homepage", "SEE_MORE": "See more", "HIDE_PREMIUM_TOOLS_DETAIL": "Show less"}