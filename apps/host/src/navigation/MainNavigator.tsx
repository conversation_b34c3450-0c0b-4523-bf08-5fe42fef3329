import React from 'react';
import { RouteName } from '@btaskee/design-system';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { AuthScreen } from '@screens/Auth';
import { BlacklistTaskerScreen } from '@screens/BlacklistTasker';
import { BPayScreen } from '@screens/bPay';
import { BRewardScreen } from '@screens/bReward';
import { ComboVoucherScreen } from '@screens/ComboVoucher';
import { ExploreBTaskeeScreen } from '@screens/ExploreBtaskee';
import { FavTaskerScreen } from '@screens/FavTasker';
import { HelpCenterScreen } from '@screens/HelpCenter';
import { IntroExploreBTaskeeScreen } from '@screens/IntroExploreBtaskee';
import { MyGiftScreen } from '@screens/MyGift';
import { PaymentScreen } from '@screens/Payment';
import { ProfileScreen } from '@screens/Profile';
import { ReferralCodeScreen } from '@screens/ReferralCode';
import { AirConditionerScreen } from '@screens/Services/AirConditioner';
import { ChildCareScreen } from '@screens/Services/ChildCare';
import { ChildCareSubscriptionScreen } from '@screens/Services/ChildCareSubscription';
import { CleaningScreen } from '@screens/Services/Cleaning';
import { CleaningSubscriptionScreen } from '@screens/Services/CleaningSubscription';
import { DeepCleaningScreen } from '@screens/Services/DeepCleaning';
import { DisinfectionScreen } from '@screens/Services/Disinfection';
import { ElderlyCareScreen } from '@screens/Services/ElderlyCare';
import { ElderlyCareSubscriptionScreen } from '@screens/Services/ElderlyCareSubscription';
import { HomeCookingScreen } from '@screens/Services/HomeCooking';
import { OfficeCarpetCleaningScreen } from '@screens/Services/OfficeCarpetCleaning';
import { OfficeCleaningScreen } from '@screens/Services/OfficeCleaning';
import { OfficeCleaningSubscriptionScreen } from '@screens/Services/OfficeCleaningSubscription';
import { PatientCareScreen } from '@screens/Services/PatientCare';
import { PatientCareSubscriptionScreen } from '@screens/Services/PatientCareSubscription';
import { WashingMachineScreen } from '@screens/Services/WashingMachine';
import { WaterHeaterScreen } from '@screens/Services/WaterHeater';
import { SettingScreen } from '@screens/Setting';
import { TaskManagementScreen } from '@screens/TaskManagements';
import { VoiceChatScreen } from '@screens/VoiceChat';

import { TabsNavigator } from './TabNavigator';
import { MainStackParamList } from './type';

const Main = createNativeStackNavigator<MainStackParamList>();

export const MainNavigator = () => {
  return (
    <Main.Navigator screenOptions={{ headerBackTitle: 'Trở về' }}>
      <Main.Screen
        name={RouteName.TabNavigator}
        component={TabsNavigator}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.OfficeCleaningService}
        component={OfficeCleaningScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.OfficeCleaningSubscriptionService}
        component={OfficeCleaningSubscriptionScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.CleaningSubscriptionService}
        component={CleaningSubscriptionScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.DeepCleaningService}
        component={DeepCleaningScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.CleaningService}
        component={CleaningScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.AirConditionerService}
        component={AirConditionerScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.ChildCareService}
        component={ChildCareScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.ChildCareSubscriptionService}
        component={ChildCareSubscriptionScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.ElderlyCareService}
        component={ElderlyCareScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.PatientCareService}
        component={PatientCareScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.VoiceChat}
        component={VoiceChatScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.Auth}
        component={AuthScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.Payment}
        component={PaymentScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.Setting}
        component={SettingScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.HelpCenter}
        component={HelpCenterScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.FavTasker}
        component={FavTaskerScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.BlacklistTasker}
        component={BlacklistTaskerScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.ReferralCode}
        component={ReferralCodeScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.ComboVoucher}
        component={ComboVoucherScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.bReward}
        component={BRewardScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.bPay}
        component={BPayScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.MyGift}
        component={MyGiftScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.Profile}
        component={ProfileScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.ElderlyCareSubscriptionService}
        component={ElderlyCareSubscriptionScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.PatientCareSubscriptionService}
        component={PatientCareSubscriptionScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.TaskManagement}
        component={TaskManagementScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.ExploreBTaskee}
        component={ExploreBTaskeeScreen}
        options={{ headerShown: true }}
      />
      <Main.Screen
        name={RouteName.IntroExploreBTaskee}
        component={IntroExploreBTaskeeScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.WaterHeaterService}
        component={WaterHeaterScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.WashingMachineService}
        component={WashingMachineScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.DisinfectionService}
        component={DisinfectionScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.HomeCookingService}
        component={HomeCookingScreen}
        options={{ headerShown: false }}
      />
      <Main.Screen
        name={RouteName.OfficeCarpetCleaningService}
        component={OfficeCarpetCleaningScreen}
        options={{ headerShown: false }}
      />
    </Main.Navigator>
  );
};
