import React from 'react';
import { RouteName, useAppStore } from '@btaskee/design-system';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { AppWithHOC } from './AppWithHOC';
import { IntroNavigator } from './IntroNavigator';
import { RootStackParamList } from './type';

const Root = createNativeStackNavigator<RootStackParamList>();

export const RootNavigator = () => {
  const { isFirstOpen } = useAppStore();

  return (
    <Root.Navigator
      initialRouteName={
        isFirstOpen ? RouteName.IntroNavigator : RouteName.MainNavigator
      }
      screenOptions={{ headerShown: false }}
    >
      <Root.Screen
        name={RouteName.IntroNavigator}
        component={IntroNavigator}
      />
      <Root.Screen
        name={RouteName.MainNavigator}
        component={AppWithHOC}
      />
    </Root.Navigator>
  );
};
