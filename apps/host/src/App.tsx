import React, { useMemo } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import {
  navigationRef,
  QueryClient,
  QueryProvider,
} from '@btaskee/design-system';
import { NavigationContainer } from '@react-navigation/native';

import { RootNavigator } from '@navigation/RootNavigator';

const App = () => {
  const containerStyle = useMemo<StyleProp<ViewStyle>>(() => ({ flex: 1 }), []);
  const queryClient = new QueryClient();

  return (
    <SafeAreaProvider>
      <QueryProvider client={queryClient}>
        <GestureHandlerRootView style={containerStyle}>
          <KeyboardProvider>
            <NavigationContainer ref={navigationRef as any}>
              <RootNavigator />
            </NavigationContainer>
          </KeyboardProvider>
        </GestureHandlerRootView>
      </QueryProvider>
    </SafeAreaProvider>
  );
};

export default App;
